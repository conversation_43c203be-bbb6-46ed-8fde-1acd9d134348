<?php
session_start();

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    
    if (empty($email)) {
        $message = 'Por favor, digite seu e-mail.';
        $message_type = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Por favor, digite um e-mail válido.';
        $message_type = 'error';
    } else {
        // Simulate password recovery (in production, send actual email)
        $message = 'Se o e-mail estiver cadastrado, você receberá instruções para recuperar sua senha.';
        $message_type = 'success';
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recuperar Senha - GERADOR PRO</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --text-light: rgba(255, 255, 255, 0.95);
            --neon-blue: #08f;
            --neon-purple: #a0f;
            --neon-pink: #f0a;
            --neon-cyan: #0ff;
            --neon-orange: #ff8c00;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            min-height: 100vh;
            background: linear-gradient(135deg, #0a0a10 0%, #1a1a2e 50%, #16213e 100%);
            color: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .bubbles {
            position: fixed;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
            top: 0;
            left: 0;
            filter: blur(50px);
            opacity: 0.7;
        }
        
        .bubble {
            position: absolute;
            border-radius: 50%;
            animation: rise 22.5s linear infinite;
            opacity: 0;
            mix-blend-mode: screen;  
        }
        
        @keyframes rise {
            0% {
                transform: translateY(110vh) scale(0.8);
                opacity: 0;
            }
            20% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-10vh) scale(1.2);
                opacity: 0;
            }
        }
        
        .recovery-container {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.2),
                0 0 40px rgba(160, 0, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.15);
            width: 100%;
            max-width: 450px;
            animation: slideInUp 0.8s ease-out;
            z-index: 10;
            position: relative;
        }
        
        @keyframes slideInUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo-title {
            font-size: 2.5rem;
            font-weight: 600;
            background: linear-gradient(45deg, var(--neon-cyan), var(--neon-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }
        
        .logo-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            font-weight: 300;
        }
        
        .recovery-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
            color: var(--text-light);
        }
        
        .recovery-description {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 25px;
            line-height: 1.5;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-light);
            font-weight: 400;
            font-size: 0.9rem;
        }
        
        input[type="email"] {
            width: 100%;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            outline: none;
        }
        
        input[type="email"]:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--neon-cyan);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .btn-recovery {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, var(--neon-purple), var(--neon-blue));
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 20px;
        }
        
        .btn-recovery:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(160, 0, 255, 0.4);
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            animation: slideIn 0.3s ease-out;
        }
        
        .alert-error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff6b6b;
        }
        
        .alert-success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            color: #4ade80;
        }
        
        .back-to-login {
            text-align: center;
        }
        
        .back-to-login a {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .back-to-login a:hover {
            color: var(--neon-cyan);
        }
        
        .footer-text {
            text-align: center;
            margin-top: 20px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.8rem;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(-20px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="bubbles">
        <div class="bubble" style="left: 20%; width: 200px; height: 200px; animation-delay: 2s; animation-duration: 25s; background: rgba(0, 255, 255, 0.6);"></div>
        <div class="bubble" style="left: 70%; width: 150px; height: 150px; animation-delay: 5s; animation-duration: 30s; background: rgba(160, 0, 255, 0.6);"></div>
        <div class="bubble" style="left: 50%; width: 180px; height: 180px; animation-delay: 1s; animation-duration: 20s; background: rgba(255, 0, 160, 0.6);"></div>
    </div>

    <div class="recovery-container">
        <div class="logo-section">
            <div class="logo-title">GERADOR PRO</div>
            <p class="logo-subtitle">Recuperação de Senha</p>
        </div>
        
        <h2 class="recovery-title">Esqueceu sua senha?</h2>
        <p class="recovery-description">
            Digite seu e-mail abaixo e enviaremos instruções para redefinir sua senha.
        </p>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="recu_senha.php" id="recovery-form">
            <div class="form-group">
                <label for="email">E-mail:</label>
                <input type="email" id="email" name="email" required placeholder="Digite seu e-mail" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
            </div>

            <button type="submit" class="btn-recovery">Enviar Instruções</button>
        </form>

        <div class="back-to-login">
            <a href="login.php">← Voltar ao Login</a>
        </div>

        <div class="footer-text">
            © 2025 GERADOR PRO - Todos os direitos reservados
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const emailField = document.getElementById('email');
            emailField.focus();
        });

        let formSubmitted = false;
        const recoveryForm = document.getElementById('recovery-form');

        recoveryForm.addEventListener('submit', function(e) {
            if (formSubmitted) {
                e.preventDefault();
                return;
            }
            
            formSubmitted = true;
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.textContent = 'Enviando...';
                submitBtn.disabled = true;
            }
        });
    </script>
</body>
</html>
