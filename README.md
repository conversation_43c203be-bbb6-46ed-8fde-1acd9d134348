# GERADOR PRO - Sistema de Login e Dashboard

Um sistema moderno de login e dashboard com design neon e animações fluidas.

## 🚀 Características

- **Design Moderno**: Interface com tema neon e efeitos de glassmorphism
- **Animações Fluidas**: Bolhas animadas e transições suaves
- **Responsivo**: Funciona perfeitamente em desktop e mobile
- **Sistema de Login**: Autenticação básica com PHP e sessões
- **Dashboard Interativo**: Painel administrativo com sidebar navegável
- **Recuperação de Senha**: Sistema básico de recuperação de senha

## 📁 Estrutura do Projeto

```
baner/
├── index.php              # Página inicial (redireciona para login)
├── login.php              # Página de login com autenticação
├── login.html             # Versão estática da página de login
├── recu_senha.php         # Página de recuperação de senha
├── dashboard.html         # Dashboard principal
└── README.md              # Este arquivo
```

## 🔐 Credenciais de Teste

Para testar o sistema, use uma das seguintes credenciais:

- **admin** / **admin123**
- **user** / **user123**
- **demo** / **demo123**

## 🛠️ Instalação e Uso

### Requisitos
- Servidor web (Apache/Nginx)
- PHP 7.4 ou superior
- Navegador moderno

### Instalação
1. Clone ou baixe os arquivos para seu servidor web
2. Certifique-se de que o PHP está funcionando
3. Acesse `index.php` no seu navegador
4. Use uma das credenciais de teste para fazer login

### Desenvolvimento Local
Se você tem o PHP instalado localmente:

```bash
# Navegue até a pasta do projeto
cd baner

# Inicie o servidor PHP local
php -S localhost:8000

# Acesse no navegador
http://localhost:8000
```

## 🎨 Design e Funcionalidades

### Página de Login
- Design com tema neon e cores vibrantes
- Animações de bolhas em movimento
- Efeito glassmorphism nos containers
- Validação de formulário
- Mensagens de erro elegantes
- Link para recuperação de senha

### Dashboard
- Sidebar responsiva com navegação
- Cards interativos para diferentes seções
- Design consistente com a página de login
- Menu mobile com toggle
- Badges para funcionalidades "Em Breve"

### Recuperação de Senha
- Interface consistente com o resto do sistema
- Validação de e-mail
- Mensagens de feedback
- Link para voltar ao login

## 🔧 Personalização

### Cores
As cores principais estão definidas em variáveis CSS no `:root`:

```css
:root {
    --text-light: rgba(255, 255, 255, 0.95);
    --neon-blue: #08f;
    --neon-purple: #a0f;
    --neon-cyan: #0ff;
    --page-bg: #0D1117;
    --sidebar-bg: #16213e;
    --card-bg: #16213e;
}
```

### Autenticação
Para usar com um banco de dados real, modifique o array `$valid_users` em `login.php` e implemente a verificação no banco de dados.

### Funcionalidades Adicionais
O dashboard está preparado para receber novas páginas:
- `generators.html` - Página de geradores
- `templates.html` - Página de templates
- `users.html` - Gerenciamento de usuários
- `analytics.html` - Página de analytics
- `settings.html` - Configurações do sistema

## 📱 Responsividade

O sistema é totalmente responsivo e inclui:
- Menu mobile com botão toggle
- Layout adaptativo para tablets e smartphones
- Sidebar que se esconde automaticamente em telas pequenas
- Cards que se reorganizam em grid responsivo

## 🔒 Segurança

**Nota**: Este é um sistema de demonstração. Para uso em produção, implemente:
- Hash de senhas (bcrypt/password_hash)
- Proteção CSRF
- Validação mais robusta
- Conexão com banco de dados
- Rate limiting para tentativas de login
- HTTPS obrigatório

## 📄 Licença

Este projeto é fornecido como exemplo educacional. Use e modifique conforme necessário.

---

**GERADOR PRO** © 2025 - Sistema de Login e Dashboard
