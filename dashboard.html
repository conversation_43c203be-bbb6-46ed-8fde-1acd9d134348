<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#16213e">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Dashboard - Painel</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        /* Reset e Variáveis CSS */
        :root {
            --sidebar-width: 260px;
            --sidebar-width-collapsed: 80px;
            --page-bg: #0D1117; /* Fundo mais escuro */
            --sidebar-bg: #16213e; /* Fundo da sidebar */
            --card-bg: #16213e; /* Fundo dos cards */
            --border-color: #30363D;
            --text-color: #f0f0f0;
            --text-muted: #8B949E;
            --accent-color: #4e73df;
            --danger-color: #dc3545;
            --transition-speed: 0.3s;
            --border-radius: 10px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Estilos Base */
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--page-bg);
            color: var(--text-color);
            min-height: 100vh;
            line-height: 1.6;
        }

        /* Layout Principal */
        .page-wrapper {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* Sidebar */
        #sidebar {
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
            transition: all var(--transition-speed) ease;
            display: flex;
            flex-direction: column; /* Coloca os filhos em coluna */
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1003;
        }

        #sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            min-height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex-shrink: 0; /* Impede que o cabeçalho encolha */
            position: relative; /* NOVO: Contexto para o botão de fechar */
        }

        .logo-title {
            font-size: 1.8rem;
            font-weight: 700;
            letter-spacing: 1px;
        }

        .logo-part-1 {
            color: #3B82F6; /* Azul do logo */
        }

        .logo-part-2 {
            color: #FACC15; /* Amarelo/Ouro do logo */
        }

        .logo-subtitle {
            font-size: 0.9rem;
            color: var(--text-muted);
            margin-top: 5px;
        }

        #sidebar-content {
            flex-grow: 1; /* Ocupa todo o espaço restante */
            padding: 15px 10px;
            overflow-y: auto; /* Adiciona rolagem se o conteúdo for muito longo */
            -ms-overflow-style: none;
            scrollbar-width: none;
            padding-bottom: 80px;
        }

        #sidebar-content::-webkit-scrollbar {
            display: none;
        }


        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 14px 20px;
            color: var(--text-muted);
            text-decoration: none;
            transition: all var(--transition-speed);
            font-weight: 500;
            margin: 4px 10px;
            border-radius: var(--border-radius);
            white-space: nowrap;
        }

        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
        }

        .sidebar-item.active {
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
            color: #fff;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .sidebar-item.active i {
            color: #fff;
        }

        .sidebar-item i {
            width: 24px;
            margin-right: 15px;
            font-size: 1.1rem;
            text-align: center;
            color: var(--text-muted);
            transition: color var(--transition-speed);
        }

        #sidebar-footer {
            padding: 10px 15px;
            border-top: 1px solid var(--border-color);
            flex-shrink: 0;
            position: sticky;
            bottom: 0;
            background: var(--sidebar-bg);
            z-index: 10;
        }

        .sidebar-item.logout {
            margin: 0;
            padding: 14px 20px;
            border-radius: var(--border-radius);
        }

        .sidebar-item.logout:hover {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
        }

        .soon-badge {
            font-size: 0.65rem;
            font-weight: 700;
            text-transform: uppercase;
            color: #FACC15;
            background-color: rgba(250, 204, 21, 0.1);
            padding: 3px 7px;
            border-radius: 5px;
            margin-left: auto;
        }

        /* Conteúdo Principal */
        #main-content {
            flex-grow: 1;
            margin-left: var(--sidebar-width);
            padding: 25px;
            transition: margin-left var(--transition-speed);
            min-height: 100vh;
        }

        /* Cabeçalho do Conteúdo da Página */
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 30px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 30px;
        }

        .welcome-message h1 {
            font-size: 1.8rem;
            font-weight: 600;
        }
        .welcome-message p {
            color: var(--text-muted);
            font-size: 1rem;
        }

        /* Grid do Dashboard (usado apenas em index.php) */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .action-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 25px;
            text-decoration: none;
            color: var(--text-color);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 150px;
        }

        .action-card:hover {
            transform: translateY(-5px);
            border-color: var(--accent-color);
        }

        .card-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--text-muted);
        }

        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #fff;
        }
        
        /* Cores dos Ícones */
        .icon-blue { background: linear-gradient(145deg, #2563EB, #3B82F6); }
        .icon-green { background: linear-gradient(145deg, #16A34A, #22C55E); }
        .icon-purple { background: linear-gradient(145deg, #7C3AED, #9333EA); }
        .icon-orange { background: linear-gradient(145deg, #EA580C, #F97316); }
        .icon-red { background: linear-gradient(145deg, #DC2626, #EF4444); }
        .icon-yellow { background: linear-gradient(145deg, #EAB308, #FACC15); }
        .icon-cyan { background: linear-gradient(145deg, #0891B2, #06B6D4); }

        .card-main-content {
            margin-top: 15px;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            #sidebar {
                transform: translateX(-100%);
            }

            #sidebar.active {
                transform: translateX(0);
            }

            #main-content {
                margin-left: 0;
                padding: 15px;
            }

            .main-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1004;
            background: var(--sidebar-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="page-wrapper">
        <!-- Mobile Menu Button -->
        <button class="mobile-menu-btn" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Sidebar -->
        <nav id="sidebar">
            <div id="sidebar-header">
                <div class="logo-title">
                    <span class="logo-part-1">GERADOR</span>
                    <span class="logo-part-2">PRO</span>
                </div>
                <p class="logo-subtitle">Painel Administrativo</p>
            </div>

            <div id="sidebar-content">
                <a href="dashboard.html" class="sidebar-item active">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
                <a href="generators.html" class="sidebar-item">
                    <i class="fas fa-cogs"></i>
                    Geradores
                </a>
                <a href="templates.html" class="sidebar-item">
                    <i class="fas fa-file-alt"></i>
                    Templates
                </a>
                <a href="users.html" class="sidebar-item">
                    <i class="fas fa-users"></i>
                    Usuários
                </a>
                <a href="analytics.html" class="sidebar-item">
                    <i class="fas fa-chart-bar"></i>
                    Analytics
                    <span class="soon-badge">Em Breve</span>
                </a>
                <a href="settings.html" class="sidebar-item">
                    <i class="fas fa-cog"></i>
                    Configurações
                </a>
            </div>

            <div id="sidebar-footer">
                <a href="login.html" class="sidebar-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    Sair
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main id="main-content">
            <div class="main-header">
                <div class="welcome-message">
                    <h1>Dashboard</h1>
                    <p>Bem-vindo ao painel administrativo do GERADOR PRO</p>
                </div>
            </div>

            <!-- Dashboard Grid -->
            <div class="dashboard-grid">
                <a href="generators.html" class="action-card">
                    <div class="card-top">
                        <div class="card-title">Geradores</div>
                        <div class="card-icon icon-blue">
                            <i class="fas fa-cogs"></i>
                        </div>
                    </div>
                    <div class="card-main-content">
                        <p>Gerencie e configure seus geradores de conteúdo</p>
                    </div>
                </a>

                <a href="templates.html" class="action-card">
                    <div class="card-top">
                        <div class="card-title">Templates</div>
                        <div class="card-icon icon-green">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div class="card-main-content">
                        <p>Crie e edite templates personalizados</p>
                    </div>
                </a>

                <a href="users.html" class="action-card">
                    <div class="card-top">
                        <div class="card-title">Usuários</div>
                        <div class="card-icon icon-purple">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="card-main-content">
                        <p>Gerencie usuários e permissões</p>
                    </div>
                </a>

                <a href="analytics.html" class="action-card">
                    <div class="card-top">
                        <div class="card-title">Analytics</div>
                        <div class="card-icon icon-orange">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                    </div>
                    <div class="card-main-content">
                        <p>Visualize estatísticas e relatórios</p>
                        <span class="soon-badge">Em Breve</span>
                    </div>
                </a>

                <a href="settings.html" class="action-card">
                    <div class="card-top">
                        <div class="card-title">Configurações</div>
                        <div class="card-icon icon-cyan">
                            <i class="fas fa-cog"></i>
                        </div>
                    </div>
                    <div class="card-main-content">
                        <p>Configure as opções do sistema</p>
                    </div>
                </a>

                <a href="#" class="action-card">
                    <div class="card-top">
                        <div class="card-title">Suporte</div>
                        <div class="card-icon icon-yellow">
                            <i class="fas fa-life-ring"></i>
                        </div>
                    </div>
                    <div class="card-main-content">
                        <p>Obtenha ajuda e suporte técnico</p>
                    </div>
                </a>
            </div>
        </main>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            if (window.innerWidth <= 768) {
                if (!sidebar.contains(event.target) && !menuBtn.contains(event.target)) {
                    sidebar.classList.remove('active');
                }
            }
        });
    </script>
</body>
</html>
