<?php
session_start();

// Simple authentication (replace with database authentication in production)
$valid_users = [
    'admin' => 'admin123',
    'user' => 'user123',
    'demo' => 'demo123'
];

$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    // Basic validation
    if (empty($username) || empty($password)) {
        $error_message = 'Por favor, preencha todos os campos.';
    } elseif (isset($valid_users[$username]) && $valid_users[$username] === $password) {
        // Successful login
        $_SESSION['user_logged_in'] = true;
        $_SESSION['username'] = $username;
        $_SESSION['login_time'] = time();
        
        // Redirect to dashboard
        header('Location: dashboard.html');
        exit;
    } else {
        $error_message = '<PERSON><PERSON><PERSON><PERSON> ou senha incorretos.';
    }
}
?>
<!DOCTYPE html> 
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - GERADOR PRO</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --text-light: rgba(255, 255, 255, 0.95);
            --neon-blue: #08f;
            --neon-purple: #a0f;
            --neon-pink: #f0a;
            --neon-cyan: #0ff;
            --neon-orange: #ff8c00;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            min-height: 100vh;
            background: linear-gradient(135deg, #0a0a10 0%, #1a1a2e 50%, #16213e 100%);
            color: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .bubbles {
            position: fixed;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
            top: 0;
            left: 0;
            filter: blur(50px);
            opacity: 0.7;
        }
        
        .bubble {
            position: absolute;
            border-radius: 50%;
            animation: rise 22.5s linear infinite;
            opacity: 0;
            mix-blend-mode: screen;  
        }
        
        @keyframes rise {
            0% {
                transform: translateY(110vh) scale(0.8);
                opacity: 0;
            }
            20% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-10vh) scale(1.2);
                opacity: 0;
            }
        }
        
        .main-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 60px;
            width: 100%;
            max-width: 900px;
            z-index: 10;
            position: relative;
        }

        .info-section {
            flex: 1;
            text-align: left;
            padding-right: 40px;
        }

        .info-section img {
            max-width: 250px;
            height: auto;
            margin-bottom: 25px;
            filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.3));
        }

        .invitation-title {
            font-size: 2.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-light);
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .invitation-text {
            font-size: 1rem;
            font-weight: 300;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
        }

        .login-section {
            flex: 1;
            max-width: 400px;
            margin-top: 65px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.2),
                0 0 40px rgba(160, 0, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.15);
            width: 100%;
            animation: slideInUp 0.8s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo-title {
            font-size: 2.5rem;
            font-weight: 600;
            background: linear-gradient(45deg, var(--neon-cyan), var(--neon-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }
        
        .logo-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            font-weight: 300;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-light);
            font-weight: 400;
            font-size: 0.9rem;
        }
        
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            outline: none;
        }
        
        input[type="text"]:focus,
        input[type="password"]:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--neon-cyan);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .btn-login {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, var(--neon-purple), var(--neon-blue));
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(160, 0, 255, 0.4);
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            animation: slideIn 0.3s ease-out;
            transition: opacity 0.3s ease-out;
        }
        
        .alert-error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff6b6b;
        }
        
        .forgot-password {
            text-align: right;
            margin-top: -5px;
            margin-bottom: 15px;
        }
        .forgot-password a {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            font-size: 0.85rem;
            transition: color 0.3s ease;
        }
        .forgot-password a:hover {
            color: var(--neon-cyan);
        }
        
        .footer-text {
            text-align: center;
            margin-top: 20px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.8rem;
        }
        
        /* Responsivo */
        @media (max-width: 768px) {
            body {
                align-items: flex-start;
            }
            .main-container {
                flex-direction: column;
                gap: 40px;
            }
            .info-section {
                text-align: center;
                padding-right: 0;
            }
            .login-section {
                margin-top: 0;
            }
            .info-section img {
                max-width: 200px;
            }
            .invitation-title {
                font-size: 1.8rem;
            }
            .invitation-text {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="bubbles">
        <!-- Animated bubbles -->
        <div class="bubble" style="left: 38%; width: 315px; height: 315px; animation-delay: 5s; animation-duration: 33s; background: rgba(255, 187, 0, 0.6);"></div>
        <div class="bubble" style="left: 30%; width: 372px; height: 372px; animation-delay: 3s; animation-duration: 21s; background: rgba(0, 255, 255, 0.6);"></div>
        <div class="bubble" style="left: 62%; width: 184px; height: 184px; animation-delay: 0s; animation-duration: 28s; background: rgba(0, 255, 255, 0.6);"></div>
    </div>

    <div class="main-container">
        <div class="info-section">
            <img src="https://developer.gerador.pro/assets/logo.png" alt="Logo GERADOR PRO" onerror="this.onerror=null;this.src='https://placehold.co/250x100/1a1a2e/ffffff?text=GERADOR+PRO';">
            <h2 class="invitation-title">Acesso Exclusivo à Plataforma</h2>
            <p class="invitation-text">
                Credenciais de teste:<br>
                <strong>admin / admin123</strong><br>
                <strong>user / user123</strong><br>
                <strong>demo / demo123</strong>
            </p>
        </div>

        <div class="login-section">
            <div class="login-container">
                <div class="logo-section">
                    <p class="logo-subtitle">
                        Faça login para acessar o painel
                    </p>
                </div>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-error">
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="login.php" id="login-form">
                    <div class="form-group">
                        <label for="username">Usuário:</label>
                        <input type="text" id="username" name="username" required placeholder="Digite seu usuário" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="password">Senha:</label>
                        <input type="password" id="password" name="password" required placeholder="Digite sua senha">
                    </div>

                    <div class="forgot-password">
                        <a href="recu_senha.php">Esqueceu sua senha?</a>
                    </div>

                    <button type="submit" class="btn-login">Entrar no Painel</button>
                </form>

                <div class="footer-text">
                    © 2025 GERADOR PRO - Todos os direitos reservados
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const usuarioField = document.getElementById('username');
            usuarioField.focus();
        });

        let formSubmitted = false;
        const loginForm = document.getElementById('login-form');

        loginForm.addEventListener('submit', function(e) {
            if (formSubmitted) {
                e.preventDefault();
                return;
            }
            
            formSubmitted = true;
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.textContent = 'A entrar...';
                submitBtn.disabled = true;
            }
        });
    </script>
</body>
</html>
